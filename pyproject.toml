[tool.poetry]
name = "dh-finance"
version = "0.1.0"
description = ""
authors = ["QV <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
django = "^5.2.4"
dotenv = "^0.9.9"
djangorestframework = "^3.16.0"
djangorestframework-simplejwt = "^5.5.0"
django-rest-swagger = "^2.2.0"
drf-yasg = "^1.21.10"
django-ratelimit = "^4.1.0"
psycopg2-binary = "^2.9.10"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"